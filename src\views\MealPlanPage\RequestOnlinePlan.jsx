import { useMemo, useState } from "react";
import { Loading } from "react-daisyui";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header";
import IntroStep from "../RequestMealPlanPage/Components/IntroStep";
import OrderFinalizationStep from "../RequestMealPlanPage/OrderFinalizationStep";
import QuestionsLayout from "./../OnlinePlanQuestions/QuestionsLayout";
import DiseasesStep from "./Components/DiseasesStep";
import StartTest from "./Components/StartTest";

const RequestOnlinePlan = () => {
  const navigate = useNavigate();

  const { data: _data, isLoading } = api.MealPlans.questions.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  const [activeComponent, setActiveComponent] = useState("startTest");

  if (data?.test?.status?.key === "PENDING_ANALYSIS") {
    return <OrderFinalizationStep isOnlinePlan={true} />;
  }

  if (data?.test?.status?.key === "COMPLETE_ANALYSIS") {
    return (
      <IntroStep
        next={() => {
          navigate("/payment", { replace: true });
        }}
      />
    );
  }

  return (
    <div className="flex h-full min-h-dvh flex-col gap-4 bg-base-200 pb-6">
      <Header
        className="sticky top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">درخواست برنامه غذایی</span>
      </Header>
      {isLoading ? (
        <div className="flex w-full flex-1 items-center justify-center">
          <Loading />
        </div>
      ) : data?.test ? (
        <QuestionsLayout data={data} />
      ) : activeComponent === "startTest" ? (
        <StartTest
          onClick={() => {
            setActiveComponent("diseases");
          }}
        />
      ) : (
        <DiseasesStep />
      )}
    </div>
  );
};

export default RequestOnlinePlan;
