import clsx from "clsx";
import { Message, MessageNotif } from "iconsax-react";
import moment from "jalali-moment";
import { useMemo } from "react";
import { Loading } from "react-daisyui";
import { useNavigate, useParams } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import api from "../../api";
import Button from "../Components/Global/Button.jsx";
import Header from "../Components/Global/Header.jsx";

export default function InboxSinglePage() {
  const navigate = useNavigate();

  const params = useParams();
  const { data: _data, isLoading } = api.Inbox.detail.useQuery({
    variables: { id: params.id },
  });
  const item = useMemo(() => _data?.data, [_data]);

  return (
    <section className="flex min-h-dvh flex-col bg-base-200">
      <Header
        className="sticky top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">صندوق پیام های من</span>
      </Header>
      <div className="container flex grow flex-col py-5">
        <div className="card grow bg-base-100">
          <div className="card-body px-3 py-3">
            <div className="flex items-center gap-2 border-b border-b-base-200 pb-2">
              <div
                className={twMerge(
                  "flex h-8 w-8 shrink-0 items-center justify-center bg-base-200/50 text-secondary",
                  clsx({
                    "text-base-400": item?.read,
                  }),
                )}
              >
                {isLoading ? (
                  <>
                    <div className="skeleton size-6 rounded-lg" />
                  </>
                ) : (
                  <>
                    {item?.read ? (
                      <Message className="h-6 w-6" variant="Bold" />
                    ) : (
                      <MessageNotif className="h-6 w-6" variant="Bold" />
                    )}
                  </>
                )}
              </div>
              {isLoading ? (
                <>
                  <div className="skeleton h-2 w-20" />
                </>
              ) : (
                <>
                  <div className="font-semibold">{item?.title}</div>
                </>
              )}

              <div className="mr-auto text-xs">
                {isLoading ? (
                  <>
                    <div className="skeleton h-2 w-14" />
                  </>
                ) : (
                  <>
                    {moment().diff(moment(item?.created_at), "d")
                      ? `${moment().diff(moment(item?.created_at), "d")} روز پیش`
                      : "امروز"}
                  </>
                )}
              </div>
            </div>
            <p className="px-2 pt-1 text-sm">
              {isLoading ? (
                <>
                  <div className="skeleton mt-2 h-2 w-full" />
                  <div className="skeleton mt-2 h-2 w-full" />
                  <div className="skeleton mt-2 h-2 w-full" />
                  <div className="skeleton mt-2 h-2 w-full" />
                  <div className="skeleton mt-2 h-2 w-full" />
                  <div className="skeleton mt-2 h-2 w-1/2" />
                </>
              ) : (
                <>{item?.body}</>
              )}
            </p>
            {isLoading ? (
              <div className="btn skeleton" />
            ) : (
              <Button link={-1} variant="primary">
                بازگشت
              </Button>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
