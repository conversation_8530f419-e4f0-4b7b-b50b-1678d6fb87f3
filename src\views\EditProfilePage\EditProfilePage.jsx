import clsx from "clsx";
import { Calendar, Home, Teacher, User } from "iconsax-react";
import moment from "jalali-moment";
import { useMemo } from "react";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import api from "../../api";
import { useUser } from "../../context/user.context";
import provinces from "../../data/provinces";
import AvatarField from "../Components/Form/AvatarField";
import DateSelectField from "../Components/Form/DateSelectField";
import FormField from "../Components/Form/FormField";
import SelectField from "../Components/Form/SelectField";
import Button from "../Components/Global/Button";
import Header from "../Components/Global/Header";

export default function EditProfilePage() {
  const navigate = useNavigate();
  const { setUser } = useUser();

  const { data: _data, isLoading: isProfileLoading } =
    api.Profile.detail.useQuery();
  const profileData = useMemo(() => _data?.data, [_data]);

  const {
    data: _data1,
    isLoading: isMeLoading,
    refetch: refetchMe,
  } = api.Auth.me.useQuery();
  const meData = useMemo(() => _data1?.data, [_data1]);

  const form = useForm({
    values: {
      first_name: profileData?.user?.first_name,
      last_name: profileData?.user?.last_name,
      education: profileData?.user?.education?.key,
      birthday: profileData?.user?.birthday,
      province_id: profileData?.user?.province?.id,
      gender: profileData?.user?.gender?.key,
      married: profileData?.user?.married?.key,
    },
  });

  const { mutate: updateProfile, isLoading: isUpdateLoading } =
    api.Profile.update.useMutation({
      onSuccess: async (res) => {
        if (res.status) {
          toast.success(res.message);
          const me = await refetchMe();
          if (me.data?.status) {
            setUser(me.data?.data);
          }
          navigate(-1);
        } else {
          toast.error(res.message);
        }
      },
      onError: (error) => {
        toast.error(error?.message);
      },
    });

  const btnLoading = useMemo(() => {
    return isUpdateLoading || isMeLoading;
  }, [isUpdateLoading, isMeLoading]);

  async function submit(data) {
    const formData = new FormData();

    Object.keys(data).map((key) => {
      if (key === "avatar") {
        formData.append(key, data[key]);
      } else if (key === "birthday") {
        formData.append(
          key,
          moment(data[key]).locale("fa").format("YYYY-MM-DD"),
        );
      } else {
        formData.append(key, data[key]);
      }
    });

    updateProfile(formData);
  }

  return (
    <section className="flex min-h-dvh flex-col bg-base-200">
      <Header
        className="sticky top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">ویرایش اطلاعات شخصی</span>
      </Header>

      {isProfileLoading ? (
        <div className="mt-4 flex items-center justify-center gap-3">
          <span className="loading loading-spinner" />
          <span>درحال بارگیری اطلاعات ...</span>
        </div>
      ) : (
        <>
          <div className="container py-4">
            <div className="card bg-base-100">
              <div className="card-body gap-5 px-4 py-4">
                <AvatarField
                  name="avatar"
                  form={form}
                  defaultValue={profileData?.user?.avatar}
                />

                <FormField
                  Icon={User}
                  form={form}
                  name="first_name"
                  label="نام"
                  floatingLabel
                  rules={{
                    required: "نام خود را وارد کنید.",
                  }}
                />
                <FormField
                  Icon={User}
                  form={form}
                  name="last_name"
                  label="نام خانوادگی"
                  rules={{
                    required: "نام خانوادگی خود را وارد کنید.",
                  }}
                  floatingLabel
                />
                <SelectField
                  Icon={Teacher}
                  form={form}
                  name="education"
                  label="تحصیلات"
                  rules={{
                    required: "تحصیلات خود را وارد کنید.",
                  }}
                  floatingLabel
                  options={[
                    {
                      value: "UNDERDIPLOMA",
                      label: "زیر دیپلم",
                    },
                    {
                      value: "DIPLOMA",
                      label: "دیپلم",
                    },
                    {
                      value: "ASSOCIATE",
                      label: "کاردانی",
                    },
                    {
                      value: "BACHELOR",
                      label: "کارشناسی",
                    },
                    {
                      value: "MASTER",
                      label: "کارشناسی ارشد",
                    },
                    {
                      value: "DOCTORATE",
                      label: "دکترا",
                    },
                    {
                      value: "OTHER",
                      label: "سایر",
                    },
                  ]}
                />
                <DateSelectField
                  Icon={Calendar}
                  control={form.control}
                  name="birthday"
                  label="تاریخ تولد"
                  rules={{
                    required: "تاریخ تولد خود را وارد کنید.",
                  }}
                  floatingLabel
                />

                <SelectField
                  Icon={Home}
                  form={form}
                  name="province_id"
                  label="محل سکونت"
                  floatingLabel
                  rules={{
                    required: "محل سکونت خود را وارد کنید.",
                  }}
                  options={provinces?.map((x) => ({
                    value: x.id?.toString(),
                    label: x.name,
                  }))}
                />

                <div className="text-sm">
                  <p className="mb-1">جنسیت</p>
                  <Controller
                    render={({ field: { value, onChange } }) => (
                      <div className="grid grid-cols-3 gap-2">
                        <label
                          className={twMerge(
                            "flex items-center gap-2 rounded-xl border border-base-300 px-3 py-3",
                            clsx({
                              "border-secondary transition-all duration-300":
                                value === "MALE",
                            }),
                          )}
                        >
                          <input
                            type="radio"
                            onChange={onChange}
                            checked={value === "MALE"}
                            value="MALE"
                            className={`radio radio-xs ${
                              value === "MALE" && "radio-secondary"
                            }`}
                          />
                          <span>مرد</span>
                        </label>

                        <label
                          className={twMerge(
                            "flex items-center gap-2 rounded-xl border border-base-300 px-3 py-3",
                            clsx({
                              "border-secondary transition-all duration-300":
                                value === "FEMALE",
                            }),
                          )}
                        >
                          <input
                            type="radio"
                            onChange={onChange}
                            checked={value === "FEMALE"}
                            value="FEMALE"
                            className={`radio radio-xs ${
                              value === "FEMALE" && "radio-secondary"
                            }`}
                          />
                          <span>زن</span>
                        </label>
                      </div>
                    )}
                    name="gender"
                    control={form.control}
                  />
                </div>
                <div className="text-sm">
                  <p className="mb-1">وضعیت تاهل</p>
                  <Controller
                    render={({ field: { value, onChange } }) => (
                      <div className="grid grid-cols-3 gap-2">
                        <label
                          className={twMerge(
                            "flex items-center gap-2 rounded-xl border border-base-300 px-3 py-3",
                            clsx({
                              "border-secondary transition-all duration-300":
                                value === "SINGLE",
                            }),
                          )}
                        >
                          <input
                            type="radio"
                            onChange={onChange}
                            checked={value === "SINGLE"}
                            value="SINGLE"
                            className={`radio radio-xs ${
                              value === "SINGLE" && "radio-secondary"
                            }`}
                          />
                          <span>مجرد</span>
                        </label>
                        <label
                          className={twMerge(
                            "flex items-center gap-2 rounded-xl border border-base-300 px-3 py-3",
                            clsx({
                              "border-secondary transition-all duration-300":
                                value === "MARRIAGE",
                            }),
                          )}
                        >
                          <input
                            type="radio"
                            onChange={onChange}
                            checked={value === "MARRIAGE"}
                            value="MARRIAGE"
                            className={`radio radio-xs ${
                              value === "MARRIAGE" && "radio-secondary"
                            }`}
                          />
                          <span>متاهل</span>
                        </label>
                      </div>
                    )}
                    name="married"
                    control={form.control}
                  />
                </div>

                <Button
                  loading={btnLoading}
                  onClick={form.handleSubmit(submit)}
                  variant="primary"
                >
                  ذخیره
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </section>
  );
}
