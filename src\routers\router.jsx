import { createBrowserRouter, redirect } from "react-router-dom";
import App from "../App";
import ContactUsPage from "../views/ContactUsPage/ContactUsPage";
import EditProfilePage from "../views/EditProfilePage/EditProfilePage";
import HomeLayout from "../views/HomeLayout";
import HomePage from "../views/HomePage/HomePage";
import InboxPage from "../views/InboxPage/InboxPage";
import InboxSinglePage from "../views/InboxPage/InboxSinglePage";
import Layout from "../views/Layout";
import LoginPage from "../views/LoginPage/LoginPage";
import MealPlanPage from "../views/MealPlanPage/MealPlanPage";
import RequestOnlinePlan from "../views/MealPlanPage/RequestOnlinePlan";
import SelectAnalysisType from "../views/MealPlanPage/SelectAnalysisType";
import ProfilePage from "../views/ProfilePage/ProfilePage";
import RegisterPage from "../views/RegisterPage/RegisterPage";
import ResultStep from "../views/RequestMealPlanPage/Components/ResultStep";
import PaymentPage from "../views/RequestMealPlanPage/PaymentPage";
import PaymentResultPage from "../views/RequestMealPlanPage/PaymentResultPage";
import RequestMealPlanPage from "../views/RequestMealPlanPage/RequestMealPlanPage";
import TestCentersPage from "../views/TestCenterPage/TestCentersPage";
import HistoryPage from "../views/TestsPage/Testpage/HistoryPage";
import TestPage from "../views/TestsPage/Testpage/TestPage";
import TestsPage from "../views/TestsPage/TestsPage";
import AppPage from "./../views/AppPage/AppPage";

export const router = createBrowserRouter(
  [
    {
      Component: App,
      children: [
        {
          path: "/u/:link",
          loader: async ({ params }) => redirect(`/?invite=u-${params.link}`),
        },
        {
          path: "/b/:link",
          loader: async ({ params }) => redirect(`/?invite=b-${params.link}`),
        },
        {
          path: "/login",
          Component: LoginPage,
        },

        {
          Component: Layout,
          children: [
            {
              path: "/register",
              Component: RegisterPage,
            },
            {
              path: "/profile/account",
              Component: EditProfilePage,
            },
            {
              path: "/profile/inbox",
              Component: InboxPage,
            },
            {
              path: "/profile/inbox/:id",
              Component: InboxSinglePage,
            },
            {
              path: "/profile/contact",
              Component: ContactUsPage,
            },
            {
              path: "/tests/:id",
              Component: TestPage,
            },
            {
              path: "/tests/:id/:template",
              Component: HistoryPage,
            },
            {
              path: "/request",
              Component: RequestMealPlanPage,
            },
            {
              path: "/payment",
              Component: PaymentPage,
            },
            {
              path: "/result",
              Component: PaymentResultPage,
            },
            {
              path: "/request-step",
              Component: ResultStep,
            },
            {
              path: "/test-centers",
              Component: TestCentersPage,
            },
            {
              path: "/request-online-plan",
              Component: RequestOnlinePlan,
            },
            {
              Component: HomeLayout,
              children: [
                {
                  index: true,
                  Component: MealPlanPage,
                },
                {
                  path: "/tests",
                  Component: HomePage,
                  // Component: SelectAnalysisType,
                },

                {
                  path: "/test-records",
                  Component: TestsPage,
                },

                {
                  path: "/profile",
                  Component: ProfilePage,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      path: "/app",
      Component: AppPage,
    },
  ],
  {
    basename: "/app",
  },
);
