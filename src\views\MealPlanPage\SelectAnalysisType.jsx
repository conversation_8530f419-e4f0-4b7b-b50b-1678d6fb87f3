import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import AnalysisBox from "./Components/AnalysisBox";

const SelectAnalysisType = ({ data }) => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center bg-base-200 px-4 pt-8">
      <p className="mb-6 w-full text-right font-medium text-base-content">
        یکی از روش های زیر را انتخاب کنید.
      </p>
      <div className="flex w-full max-w-md flex-col gap-4">
        <AnalysisBox
          pattern="/app/assets/images/background/InPersonTestIcon.png"
          title="آنالیز حضوری"
          description="به کمک دستگاه بادی آنالیز"
          buttonText={
            data?.test && data.test?.type?.key === "PLACE"
              ? "درخواست برنامه غذایی"
              : "لیست مراکز"
          }
          gradient="primary"
          hasAnalysis={data?.test?.type?.key === "ONLINE"}
          onClick={() => {
            if (data?.test && data.test?.type?.key === "ONLINE") {
              navigate("/request");
            } else {
              navigate("/test-centers");
            }
          }}
        />

        <AnalysisBox
          pattern="/app/assets/images/background/OnlineTestIcon.png"
          title="آنالیز آنلاین"
          description="با انجام تست هوشمند"
          buttonText={
            data?.test && data.test?.type?.key === "ONLINE"
              ? "ادامه"
              : "شروع تست"
          }
          hasAnalysis={data?.test?.type?.key === "PLACE"}
          gradient="secondary"
          onClick={() => {
            navigate("/request-online-plan");
          }}
        />
      </div>
    </div>
  );
};

SelectAnalysisType.propTypes = {
  data: PropTypes.object,
};

export default SelectAnalysisType;
