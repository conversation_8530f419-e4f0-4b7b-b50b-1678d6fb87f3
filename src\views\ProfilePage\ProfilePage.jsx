import {
  ArrowLeft,
  DocumentText,
  Edit,
  Location,
  Logout,
  Message2,
  MessageNotif,
  Mobile,
  Share,
} from "iconsax-react";
import { toast } from "react-hot-toast";
import { Link } from "react-router-dom";
import { useUser } from "../../context/user.context.jsx";
import Button from "../Components/Global/Button.jsx";
import InviteModal from "../Components/Global/InviteModal.jsx";

export default function ProfilePage() {
  const { logout, user } = useUser();

  const links = [
    {
      id: 1,
      action: "account",
      title: "ویرایش اطلاعات شخصی",
      Icon: Edit,
    },
    {
      id: 2,
      action: "/profile/inbox",
      title: "صندوق پیام",
      Icon: MessageNotif,
    },
    {
      id: 3,
      action: () => {
        toast("این قابلیت به‌زودی فعال میشود.", {
          icon: "⚠️",
        });
      },
      title: "سوابق  تراکنش",
      Icon: DocumentText,
    },
    {
      id: 4,
      action: "/profile/contact",
      title: "ارتباط با ما",
      Icon: Message2,
    },
    {
      id: 5,
      action: async () => {
        document.getElementById("invite_modal").showModal();
      },
      title: "دعوت از دوستان",
      Icon: Share,
    },
    {
      id: 6,
      action: "/test-centers",
      title: "مراکز تست",
      Icon: Location,
    },
  ];

  return (
    <div className="flex min-h-[100svh] grow flex-col">
      <div className="relative mb-32 flex h-32 flex-col items-center bg-base-100 shadow-card">
        <div className="absolute top-16 flex flex-col items-center justify-center rounded-full">
          {/*TODO: add section role*/}
          <div className="relative">
            <img
              className="h-32 w-32 rounded-full border-8 border-base-100"
              src={user?.avatar ?? "/app/assets/images/user/user.png"}
              alt=""
            />
            <Link
              to="account"
              className="absolute bottom-0 right-0 z-10 flex aspect-square size-10 items-center justify-center rounded-full bg-base-100 text-secondary"
            >
              <Edit className="size-5" />
            </Link>
          </div>
          <span className="mt-4 text-base font-bold text-base-content">
            {`${user?.first_name} ${user?.last_name}`}
          </span>
          <div className="flex items-center gap-1 text-base-400">
            <Mobile className="h-4 w-4" />
            <p className="text-base font-normal">{user?.mobile} </p>
          </div>
        </div>
      </div>

      <div className="container grow select-none space-y-3 bg-base-200 py-4">
        {links?.map(({ Icon, ...item }) => {
          const Tag = typeof item.action === "string" ? Link : "div";
          return (
            <Tag
              key={item.id}
              {...item.props}
              {...(Tag === "div"
                ? {
                    onClick: item.action,
                  }
                : {
                    to: item.action,
                  })}
              className="my-5 flex items-center justify-between gap-3 rounded-xl bg-base-100 p-3"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-full text-secondary">
                <Icon className="h-6 w-6" />
              </div>
              <div className="grow text-sm font-medium text-base-content">
                {item.title}
              </div>
              <span>
                <ArrowLeft className="h-5 w-5 text-base-400" />
              </span>
            </Tag>
          );
        })}

        <div
          onClick={() => document.getElementById("logout").showModal()}
          className="my-5 flex items-center justify-between gap-3 rounded-xl bg-base-100 p-3"
        >
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-error/10 text-error">
            <Logout className="h-6 w-6" />
          </div>
          <div className="grow text-sm font-medium text-error">
            خروج از حساب کاربری
          </div>
          <span>
            <ArrowLeft className="h-5 w-5 text-error" />
          </span>
        </div>
        <dialog id="logout" className="modal px-5">
          <div className="modal-box p-0">
            <h3 className="flex items-center justify-center gap-2 bg-primary py-3 text-lg font-semibold text-primary-content">
              <Logout className="h-5 w-5" />
              <span>خروج</span>
            </h3>
            <p className="px-4 pt-6 text-center">
              آیا میخواهید از حساب کاربری خارج شوید؟
            </p>
            <div className="modal-action grid grid-cols-2 gap-3 px-5 pb-5">
              <form method="dialog">
                <Button variant="secondary" className="btn-outline w-full">
                  خیر
                </Button>
              </form>
              <Button
                onClick={() => {
                  logout();
                }}
                variant="secondary"
                className="w-full"
              >
                بله
              </Button>
            </div>
          </div>
        </dialog>
        <InviteModal message={user?.invite_msg ?? ""} />
      </div>
    </div>
  );
}
