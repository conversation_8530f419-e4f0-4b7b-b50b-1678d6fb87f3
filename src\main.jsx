import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { CookiesProvider } from "react-cookie";
import { Toaster } from "react-hot-toast";
import { RouterProvider } from "react-router-dom";

import ThemeContextProvider from "./context/theme.context.jsx";
import UserContextProvider from "./context/user.context.jsx";
import { router } from "./routers/router.jsx";

const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <div className="mx-auto lg:w-96">
      <ThemeContextProvider>
        <CookiesProvider>
          <UserContextProvider>
            <QueryClientProvider client={queryClient}>
              <RouterProvider router={router} />
              <Toaster position="top-center" reverseOrder={false} />
            </QueryClientProvider>
          </UserContextProvider>
        </CookiesProvider>
      </ThemeContextProvider>
    </div>
  </React.StrictMode>,
);
