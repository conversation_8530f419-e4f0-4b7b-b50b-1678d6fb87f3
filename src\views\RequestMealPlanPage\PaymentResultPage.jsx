import { useEffect, useMemo } from "react";
import { Loading } from "react-daisyui"; // Import Loading component
import { useNavigate, useSearchParams } from "react-router-dom";
import api from "../../api";

const PaymentResultPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const {
    mutate: checkPaymentStatus,
    isLoading,
    data: _data,
  } = api.Packages.paymentResult.useMutation({
    onSuccess: (res) => {
      // Handle the response data here
      if (res?.data?.payment?.status === "SUCCESS") {
        navigate("/request", { replace: true });
      }
      // If status is not SUCCESS, the component will render the failure message
    },
    onError: (error) => {
      // Handle error if needed, though the current UI just shows a generic failure
      console.error("Payment check failed:", error);
    },
  });

  const data = useMemo(() => _data?.data, [_data]); // Extract data from the response

  useEffect(() => {
    // Trigger the mutation when the component mounts
    const authority = searchParams.get("authority");
    if (authority) {
      checkPaymentStatus({ authority });
    }
  }, [searchParams, checkPaymentStatus]); // Add checkPaymentStatus to dependencies

  if (isLoading || !data) {
    return (
      <div className="flex h-dvh flex-col items-center justify-center">
        <div className="loading loading-spinner loading-lg text-primary" />
      </div>
    );
  }

  // Render failure message if data is available but status is not SUCCESS
  if (data?.payment?.status !== "SUCCESS") {
    return (
      <div className="flex h-dvh flex-col p-8 lg:max-w-96">
        <div className="flex grow flex-col items-center justify-center text-center">
          <img
            src="/app/assets/images/result/failed-result.png"
            alt="Payment Failed"
          />{" "}
          {/* Added alt text */}
          <h1 className={"mt-8 line-clamp-1 text-2xl font-bold text-secondary"}>
            پرداخت انجام نشد
          </h1>
          <h2 className="mt-2 line-clamp-3 text-sm text-base-600">
            لطفا دوباره تلاش کنید
          </h2>
        </div>

        <button
          onClick={() => {
            navigate("/request", { replace: true });
          }}
          className="btn btn-primary mt-auto"
        >
          بازگشت
        </button>
      </div>
    );
  }

  // This part should ideally not be reached if status is SUCCESS,
  // as the navigation happens in onSuccess.
  // However, keeping a fallback or success state rendering might be necessary
  // depending on the exact flow. For now, assuming navigation is the primary action on success.
  return null; // Or render a success message/component if needed
};

export default PaymentResultPage;
