import { DocumentText1 } from "iconsax-react";
import PropTypes from "prop-types";
import { useMemo } from "react";
import { Loading } from "react-daisyui";
import { useNavigate } from "react-router-dom";
import api from "../../../api";
import Header from "../../Components/Global/Header.jsx";
import Icon from "../../Components/Icon.jsx";
import IntroStepDescription from "./IntroStepDescription.jsx";
import IntroStepItem from "./IntroStepItem.jsx";

export default function IntroStep({ next }) {
  const navigate = useNavigate();
  const { data: _data, isLoading } = api.Tests.target.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  return (
    <>
      <Header
        className="sticky drop-shadow-md top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">
          تجزیه و تحلیل نتایج آنالیز بدن شما
        </span>
      </Header>

      <div className="container flex grow flex-col bg-base-200 py-4">
        <div className="card flex grow flex-col ">
          <div className="card-body grow gap-5 p-0">
            <div className="flex grow flex-col items-center">
              {isLoading && <Loading className="my-auto self-center" />}
              {!isLoading && !data?.analysis && (
                <span className="my-auto text-center">
                  {data?.message ||
                    "هدف برنامه غذایی شما درحال آماده سازیی است ، لطفا منتظر بمانید."}
                </span>
              )}
              {!isLoading && data?.analysis && (
                <div className="relative flex grow flex-col">
                  {data.analysis && (
                    <>
                      <div className="flex mb-3 items-stretch w-full gap-2">
                        <div className="flex basis-1/3 text-center items-center rounded-md flex-col bg-base-100 drop-shadow-lg py-2 px-1.5 ">
                          <span className="rounded-md bg-secondary/20 size-9 p-2 ">
                            <Icon
                              name="scale"
                              className={"size-full text-secondary"}
                            />
                          </span>

                          <p className="font-medium mt-3 text-sm ">
                            ۶ ماه آینده
                          </p>

                          <p className="text-xs">به هدف خود میرسید</p>
                        </div>
                        <div className="flex basis-1/3 text-center items-center rounded-md flex-col bg-base-100 drop-shadow-lg py-2 px-1.5 ">
                          <span className="rounded-md bg-secondary/20 size-9 p-2 ">
                            <Icon
                              name="scale-up"
                              className={"size-full text-secondary"}
                            />
                          </span>

                          <p className="font-medium mt-3 text-sm ">
                            ۶ ماه آینده
                          </p>

                          <p className="text-xs">به هدف </p>
                        </div>

                        <div className="flex basis-1/3 text-center items-center rounded-md flex-col bg-base-100 drop-shadow-lg py-2 px-1.5 ">
                          <span className="rounded-md bg-secondary/20 size-9 p-2 ">
                            <Icon
                              name="Hourglass"
                              className={"size-full text-secondary"}
                            />
                          </span>

                          <p className="font-medium mt-3 text-sm ">
                            ۶ ماه آینده
                          </p>

                          <p className="text-xs">به هدف خود میرسید</p>
                        </div>
                      </div>{" "}
                      <div className="rounded-lg bg-base-100 drop-shadow-lg p-2.5">
                        <IntroStepItem {...data?.analysis?.key_analysis} />
                        <IntroStepItem {...data?.analysis?.fitness_goals} />
                        <IntroStepDescription
                          {...data?.analysis?.recommended_actions}
                        />
                        <div className="sticky bottom-0 mt-auto h-full w-full bg-base-100 pb-2">
                          <button
                            onClick={next}
                            disabled={!data.analysis}
                            className="text-bold btn btn-primary w-full text-lg"
                          >
                            <DocumentText1 className="h-6 w-6" /> ارائه برنامه
                            غذایی
                          </button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

IntroStep.propTypes = {
  next: PropTypes.func.isRequired,
};
