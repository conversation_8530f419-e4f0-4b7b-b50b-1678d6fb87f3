import { useForm } from 'react-hook-form';
import { cn } from '../../../utils/utils';
import Icon from '../../Components/Icon';
import toast from 'react-hot-toast';
import api from '../../../api';
import { useQueryClient } from '@tanstack/react-query';

const DiseasesStep = () => {
  const Diseases = [
    {
      title: 'بیماری های متابولیک',
      items: [
        {
          id: 6,
          color: 'primary',
          image: '/app/assets/images/banner/BloodLipids.png',
          inputName: 'bloodLipids',
        },
        {
          id: 5,
          color: 'secondary',
          image: '/app/assets/images/banner/Diabetes.png',
          inputName: 'diabetes',
        },
        {
          id: 8,
          color: 'secondary',
          image: '/app/assets/images/banner/FattyLiver.png',
          inputName: 'fattyLiver',
        },
        {
          id: 7,
          color: 'primary',
          image: '/app/assets/images/banner/HighBloodPressure.png',
          inputName: 'highBloodPressure',
        },
      ],
    },
    {
      title: 'بیماری های گوارشی',
      items: [
        {
          id: 10,
          color: 'primary',
          image: '/app/assets/images/banner/GastroesophagealReflux.png',
          inputName: 'GastroesophagealReflux',
        },
        {
          id: 9,
          color: 'secondary',
          image: '/app/assets/images/banner/LrritableBowelSyndrome.png',
          inputName: 'LrritableBowelSyndrome',
        },
        {
          id: 12,
          color: 'secondary',
          image: '/app/assets/images/banner/Constipation.png',
          inputName: 'Constipation',
        },
        {
          id: 11,
          color: 'primary',
          image: '/app/assets/images/banner/StomachUlcer.png',
          inputName: 'StomachUlcer',
        },
      ],
    },
    {
      title: 'بیماری های زنان',
      items: [
        {
          id: 13,
          color: 'secondary',
          image: '/app/assets/images/banner/PolycysticOvary.png',
          inputName: 'PolycysticOvary',
        },
        {
          id: 14,
          color: 'primary',
          image: '/app/assets/images/banner/OvarianLaziness.png',
          inputName: 'OvarianLaziness',
        },
      ],
    },
  ];

  const { mutate } = api.MealPlans.tests.add.useMutation();

  const { register, handleSubmit, watch } = useForm();

  const queryClient = useQueryClient();
  const submitHandler = (data) => {
    const selectedIds = Diseases.flatMap((category) =>
      category.items
        .filter((item) => data[item.inputName])
        .map((item) => item.id),
    );

    const steps = selectedIds.join(',');

    const payload = { steps };

    mutate(payload, {
      onSuccess: () => {
        toast.success('داده‌ها با موفقیت ارسال شدند!');
        queryClient.invalidateQueries(api.MealPlans.questions.detail.queryKey);
      },
      onError: (error) => {
        toast.error('خطایی رخ داد: ' + error.message);
      },
    });
  };

  return (
    <div className='bg-base-200'>
      <div className='flex flex-col items-center gap-6 px-4 pb-5'>
        <div
          onClick={() => {
            if (Object.values(watch()).some((item) => item === true)) {
              toast.error('اگه مریضی که دیگه سالم نیستی ');
            } else {
              submitHandler({});
            }
          }}
          className='aspect-[343/109] overflow-hidden rounded-xl bg-base-100 shadow-md'>
          <img
            className='h-full w-full'
            src='/app/assets/images/banner/HealthyDiet.png'
            alt=''
          />
        </div>
        <h2 className='text-lg font-medium text-base-content'>
          برنامه غذایی ویژه افراد مبتلا به بیماری
        </h2>

        {Diseases.map((disease, index) => (
          <div
            key={index}
            className='relative grid w-full grid-cols-2 gap-2.5 rounded-lg border border-base-300 px-2 pb-2 pt-5'>
            <span className='absolute -top-3 place-self-center bg-base-200 px-1'>
              {disease.title}
            </span>

            {disease.items.map((item) => {
              return (
                <label key={item.id} className='relative'>
                  <input
                    type='checkbox'
                    className='peer hidden'
                    {...register(item.inputName)}
                  />
                  <div
                    className={cn(
                      'absolute start-1.5 top-1.5 flex size-4 items-center justify-center rounded-md border border-base-300 bg-gradient-to-t from-base-100 to-base-100 text-base-content transition-all duration-200 peer-checked:text-base-100 peer-checked:*:translate-y-0 peer-checked:*:opacity-100',
                      {
                        'peer-checked:from-primary-focus peer-checked:to-primary':
                          item.color === 'primary',
                        'peer-checked:from-secondary peer-checked:to-secondary-focus':
                          item.color === 'secondary',
                        'peer-checked:border-primary': item.color === 'primary',
                        'peer-checked:border-secondary-focus':
                          item.color === 'secondary',
                      },
                    )}>
                    <Icon
                      className={
                        'size-full translate-y-1 text-base-100 opacity-0 transition-all duration-200'
                      }
                      strokeWidth={1.5}
                      name='tick'
                    />
                  </div>
                  <div className='overflow-hidden rounded-xl bg-base-100 shadow-md'>
                    <img className='h-full w-full' src={item.image} alt='' />
                  </div>
                </label>
              );
            })}
          </div>
        ))}
      </div>

      {Object.values(watch()).some((item) => item === true) && (
        <div className='sticky inset-x-0 bottom-0 w-full bg-base-100 px-8 pb-5 pt-3'>
          <button
            type='submit'
            onClick={handleSubmit(submitHandler)}
            className='w-full rounded-full border-2 border-primary bg-base-100 py-2 text-lg font-medium text-primary'>
            شروع مسیر سلامتی
          </button>
        </div>
      )}
    </div>
  );
};

export default DiseasesStep;
