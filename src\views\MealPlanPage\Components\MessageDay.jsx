import { Message2 } from "iconsax-react";
import PropTypes from "prop-types";
import { useCallback, useRef } from "react";
import { Button, Modal } from "react-daisyui";
import { indexToText } from "../../../utils/utils.js";
import { useModal } from "./Modal.jsx";
import { PlyrHlsPlayer } from "./PlyrHlsPlayer.jsx"; 

MessageDay.propTypes = {
  message: PropTypes.shape({
    number: PropTypes.number,
    dayNotice: PropTypes.shape({
      day: PropTypes.number,
      video: PropTypes.string,
    }),
  }),
};

export default function MessageDay({ message }) {
  const playerRef = useRef(null);
  const { isOpen, toggleState } = useModal();

  const stopVideo = useCallback(() => {
    try {
      playerRef.current?.pause();
    } catch (err) {
      console.warn("Video pause error:", err);
    }
  }, []);

  const handleClose = useCallback(() => {
    toggleState();
    stopVideo();
  }, [toggleState, stopVideo]);

  return (
    <>
      <Button
        className="shadow-profileCard btn bg-base-100 p-1 text-xs font-bold"
        onClick={toggleState}
      >
        <Message2 size="20" className="text-secondary" />
        پیام روز
      </Button>

      <Modal.Legacy
        className="overflow-visible"
        open={isOpen}
        onClickBackdrop={handleClose}
      >
        <div className="sticky top-0 z-10 flex w-full items-center justify-between border-b">
          <Modal.Header className="m-0 w-full pb-1 text-center text-lg font-bold text-base-content">
            پیام روز {indexToText(message?.dayNotice?.day)} پزشک برای شما
          </Modal.Header>
          <Button onClick={handleClose} size="sm" color="ghost" shape="circle">
            ✕
          </Button>
        </div>

        <Modal.Body>
          <div className="mt-4 rounded-lg">
            {message?.dayNotice?.video ? (
              <PlyrHlsPlayer
                videoSrc={message?.dayNotice?.video}
                playerRef={playerRef}
              />
            ) : (
              <div className="flex justify-center">
                <span> هیچ پیامی برای امروز ثبت نشده است.</span>
              </div>
            )}
          </div>
        </Modal.Body>

        <Modal.Actions className="sticky bottom-0 mt-auto h-full w-full bg-base-100 py-2">
          <Button
            onClick={handleClose}
            className="text-bold btn btn-primary w-full text-lg"
          >
            بستن
          </Button>
        </Modal.Actions>
      </Modal.Legacy>
    </>
  );
}
