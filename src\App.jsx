import { Outlet } from "react-router-dom";
import ErrorBoundary from "./views/Components/ErrorBoundary.jsx";
import "react-toastify/dist/ReactToastify.css";
import { SkeletonTheme } from "react-loading-skeleton";
import { useAppInitialization } from "./hooks/useAppInitialization.js";
import LoadingPage from "./views/ErrorsPage/LoadingPage.jsx";

function App() {
  const { isLoading } = useAppInitialization();

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <ErrorBoundary>
      <SkeletonTheme baseColor="#DDDDDD" highlightColor="#CDCDCD">
        <Outlet />
      </SkeletonTheme>
    </ErrorBoundary>
  );
}

export default App;
