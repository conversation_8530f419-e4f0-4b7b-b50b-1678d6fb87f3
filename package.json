{"name": "topbrands", "private": true, "version": "1.2.1", "type": "module", "scripts": {"dev": "vite --port 3005 --host --open", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hassanmojab/react-modern-calendar-datepicker": "^3.1.7", "@tanstack/react-query": "^5.75.4", "@tanstack/react-virtual": "^3.5.0", "ag-charts-community": "^10.3.3", "ag-charts-react": "^10.3.3", "chart.js": "^4.4.5", "clsx": "^2.1.1", "daisyui": "^4.10.5", "embla-carousel": "^8.5.2", "embla-carousel-class-names": "^8.5.2", "embla-carousel-react": "^8.5.2", "faker": "^6.6.6", "framer-motion": "^12.6.5", "hls.js": "^1.6.2", "iconsax-react": "^0.0.8", "jalali-moment": "^3.3.11", "localforage": "^1.10.0", "motion": "^12.0.11", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-circle-progress-bar": "^0.1.4", "react-circular-progressbar": "^2.1.0", "react-cookie": "^8.0.1", "react-countdown": "^2.3.5", "react-daisyui": "^5.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.4", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.4.0", "react-otp-input": "^3.1.1", "react-player": "^2.16.0", "react-query-kit": "^3.3.1", "react-router-dom": "^6.23.1", "react-select": "^5.8.0", "react-slick": "^0.30.2", "react-toastify": "^10.0.5", "sass": "^1.77.8", "slick-carousel": "^1.8.1", "swiper": "^11.1.9", "tailwind-merge": "^3.3.0", "up-fetch": "^2.1.2", "uuid": "^9.0.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/typography": "^0.5.13", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "0.6.11", "tailwindcss": "^3.4.3", "vite": "^5.2.0", "vite-plugin-pwa": "^0.20.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}