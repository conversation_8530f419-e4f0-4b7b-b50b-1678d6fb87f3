import { clsx } from "clsx";
import { useCallback } from "react";
import { useCookies } from "react-cookie";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

export const AUTH_TOKEN = "AUTH_TOKEN";

export const useAuthToken = () => {
  const [cookies, setCookie, removeCookie] = useCookies([AUTH_TOKEN]);

  const set = useCallback((token) => {
    setCookie(AUTH_TOKEN, token, { path: "/" });
  }, []);

  const remove = useCallback(() => {
    removeCookie(AUTH_TOKEN, { path: "/" });
  }, []);

  return [cookies[AUTH_TOKEN], set, remove];
};

export const getAuthToken = () => {
  const cookies = document.cookie.split(";").reduce((acc, cookie) => {
    const [name, value] = cookie.trim().split("=");
    acc[name] = value;
    return acc;
  }, {});
  return cookies[AUTH_TOKEN];
};

export function getStorage() {
  return window.localStorage;
}

export function storeUserData(user) {
  return getStorage().setItem("USER_DATA", JSON.stringify(user));
}

export function getUserData() {
  return getStorage().getItem("USER_DATA")
    ? JSON.parse(getStorage().getItem("USER_DATA"))
    : null;
}

export function removeUserData() {
  return getStorage().removeItem("USER_DATA");
}

export function getDeviceId() {
  if (getStorage().getItem("_id")) {
    return getStorage().getItem("_id");
  } else {
    const id = uuidv4();
    getStorage().setItem("_id", id);
    return id;
  }
}

export function persianNumberToEnglish(number) {
  const numbers = {
    "۰": "0",
    "۱": "1",
    "۲": "2",
    "۳": "3",
    "۴": "4",
    "۵": "5",
    "۶": "6",
    "۷": "7",
    "۸": "8",
    "۹": "9",
    "١": "1",
    "٢": "2",
    "٣": "3",
    "٤": "4",
    "٥": "5",
    "٦": "6",
    "٧": "7",
    "٨": "8",
    "٩": "9",
    "٠": "0",
  };
  const output = [];
  number.split("").forEach((char) => {
    // @ts-ignore
    output.push(char in numbers ? numbers[char] : char);
  });

  return output.join("");
}

export function indexToText(number) {
  const numbers = [
    "صفر",
    "اول",
    "دوم",
    "سوم",
    "چهارم",
    "پنجم",
    "ششم",
    "هفتم",
    "هشتم",
    "نهم",
    "دهم",
    "یازدهم",
    "دوازدهم",
    "سیزدهم",
    "چهاردهم",
    "پانزدهم",
    "شانزدهم",
    "هفدهم",
    "هجدهم",
    "نوزدهم",
  ];
  const tens = [
    "",
    "",
    "بیست",
    "سی",
    "چهل",
    "پنجاه",
    "شصت",
    "هفتاد",
    "هشتاد",
    "نود",
  ];
  const hundreds = [
    "",
    "صد",
    "دویست",
    "سیصد",
    "چهارصد",
    "پانصد",
    "ششصد",
    "هفتصد",
    "هشتصد",
    "نهصد",
  ];

  if (number === 0) {
    return numbers[0];
  }

  let text = "";
  if (number >= 1000) {
    text +=
      numbers[Math.floor(number / 1000)] + ` هزار${number === 100 ? "م" : ""} `;
    number %= 1000;
  }

  if (number >= 100) {
    text += hundreds[Math.floor(number / 100)] + (number === 100 ? "م" : " ");
    number %= 100;
  }

  if (number >= 20) {
    text += tens[Math.floor(number / 10)] + (!(number % 10) ? "م" : " ");
    number %= 10;
  }

  if (number > 0) {
    text += text && number === 1 ? "یکم" : numbers[number];
  }

  return text.trim();
}

export function getMobile(mobile) {
  return mobile.replace(/^\+989/, "09");
}

export function isIos() {
  return (
    [
      "iPad Simulator",
      "iPhone Simulator",
      "iPod Simulator",
      "iPad",
      "iPhone",
      "iPod",
    ].includes(navigator.platform) ||
    // iPad on iOS 13 detection
    (navigator.userAgent.includes("Mac") && "ontouchend" in document)
  );
}

export const cn = (...props) => {
  return twMerge(clsx(props));
};

export const convertInvalidCharacter = (text) => {
  const numbers = [
    "۰",
    "۱",
    "۲",
    "۳",
    "۴",
    "۵",
    "۶",
    "۷",
    "۸",
    "۹",
    "٠",
    "١",
    "٢",
    "٣",
    "٤",
    "٥",
    "٦",
    "٧",
    "٨",
    "٩",
  ];
  const output = [];
  const chars = {
    ك: "ک",
    دِ: "د",
    بِ: "ب",
    زِ: "ز",
    ذِ: "ذ",
    شِ: "ش",
    سِ: "س",
    ى: "ی",
    ي: "ی",
  };
  text.split("").forEach((char) => {
    output.push(
      numbers.includes(char)
        ? numbers.indexOf(char) % 10
        : char in chars
          ? chars[char]
          : char,
    );
  });
  return output.join("");
};
