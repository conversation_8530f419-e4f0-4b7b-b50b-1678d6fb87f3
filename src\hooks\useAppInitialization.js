import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import api from "../api/index.js";
import { useTheme } from "../context/theme.context.jsx"; // Import theme context hook
import { useUser } from "../context/user.context.jsx";
import { getStorage, useAuthToken } from "../utils/utils.js";

/**
 * Custom hook for initializing the application
 * Handles authentication, user data, and theme settings
 */
export function useAppInitialization() {
  const { logout, setUser } = useUser();
  const [token] = useAuthToken();
  const { setSettings } = useTheme(); // Initialize theme settings

  // Query user data only when authentication token exists
  const { data: meData, isLoading: isMeLoading } = api.Auth.me.useQuery({
    enabled: !!token,
  });

  // Query application settings regardless of authentication status
  const { data: settingsData, isLoading: isSettingsLoading } =
    api.Settings.detail.useQuery();

  const navigate = useNavigate();
  const location = useLocation();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  // Track loading state for both authentication and settings initialization
  const [isLoading, setIsLoading] = useState(true);

  // Handle initial authentication check and invite code processing
  useEffect(() => {
    async function checkAuth() {
      // Process invite code if present in URL
      if (new URLSearchParams(location.search).get("invite")) {
        await getStorage().setItem(
          "INVITE_CODE",
          new URLSearchParams(location.search).get("invite"),
        );
      }

      // Redirect to login if no authentication token exists
      if (!token) {
        navigate("/login");
        setIsAuthenticated(false);
        return;
      }
    }

    checkAuth();
  }, [token, location.search]);

  // Process user data and manage authentication state
  useEffect(() => {
    if (!isMeLoading) {
      if (meData?.status) {
        setUser(meData.data);
        setIsAuthenticated(true);
      } else {
        // Clear session and redirect on authentication failure
        logout();
        navigate("/login");
        setIsAuthenticated(false);
      }
    }
  }, [isMeLoading, meData]);

  // Initialize application settings when data is available
  useEffect(() => {
    if (!isSettingsLoading && settingsData?.status) {
      setSettings(settingsData.data);
    } else if (!isSettingsLoading && !settingsData?.status) {
      console.error("Failed to fetch settings:", settingsData?.message);
    }
  }, [isSettingsLoading, settingsData]);

  // Manage global loading state based on data fetching progress
  useEffect(() => {
    setIsLoading(isMeLoading || isSettingsLoading);
  }, [isMeLoading, isSettingsLoading]);

  return { isAuthenticated, isLoading };
}
