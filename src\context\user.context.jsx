import moment from "jalali-moment";
import PropTypes from "prop-types";
import { createContext, useContext, useState } from "react";
import {
  getUserData,
  removeUserData,
  storeUserData,
  useAuthToken,
} from "../utils/utils.js";

export const UserContext = createContext({
  user: getUserData(),
  login: (user, token) => {},
  logout: () => {},
  setUser: (user) => {},
});

export default function UserContextProvider(props) {
  const context = useContext(UserContext);
  const [state, setState] = useState(context);
  const [, setCookie, removeCookie] = useAuthToken();

  function login(user, token) {
    setCookie(token);
    storeUserData({
      ...user,
      age: moment().diff(moment(user.birthday), "year"),
    });
    setState({
      ...state,
      user: {
        ...user,
        age: moment().diff(moment(user.birthday), "year"),
      },
    });
  }
  function logout() {
    removeUserData();
    removeCookie();

    setState((prev) => ({
      ...prev,
      user: null,
    }));
  }
  function setUser(user) {
    storeUserData({
      ...user,
      age: moment().diff(moment(user.birthday), "year"),
    });
    setState({
      ...state,
      user: {
        ...user,
        age: moment().diff(moment(user.birthday), "year"),
      },
    });
  }
  return (
    <UserContext.Provider value={{ ...state, login, setUser, logout }}>
      {props.children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  return context;
}
UserContextProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
