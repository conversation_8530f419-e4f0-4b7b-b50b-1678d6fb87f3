import moment from "jalali-moment";
import PropTypes from "prop-types";
import { useId, useRef } from "react";
import { Controller, useController } from "react-hook-form";
import { cn } from "../../../utils/utils";
import Icon from "../Icon";
import DateSelectDialog from "./DateSelectDialog";

export default function DateSelectField({
  control,
  rules,
  placeholder = "",
  name,
  label,
  iconName,
  iconClassName,
  color = "primary",
  iconPlacement = "left",
  inputClassName,
  className,
  required,
}) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules: {
      required: {
        value: required,
        message: "این سوال الزامی است.",
      },
      ...rules,
    },
  });

  const id = useId();
  const ref = useRef();

  return (
    <>
      <Controller
        render={({ field: { value } }) => {
          return (
            <div className={cn("form-control w-full", className)}>
              <div className="relative">
                <input
                  {...field}
                  value={
                    value ? moment(value).locale("fa").format("YYYY/MM/DD") : ""
                  }
                  placeholder={placeholder}
                  readOnly={true}
                  ref={ref}
                  id={id}
                  onClick={() =>
                    document.getElementById(`${id}dialog`).showModal()
                  }
                  className={cn(
                    "peer input input-bordered w-full rounded-xl py-6 transition-all duration-300",
                    {
                      "focus:border-primary focus:outline-primary":
                        color === "primary",
                      "focus:border-secondary focus:outline-secondary":
                        color === "secondary",
                      "focus:border-warning focus:outline-warning":
                        color === "warning",
                      "border-error outline-error": error,
                      "focus:border-success focus:outline-success":
                        color === "success",
                      "focus:border-info focus:outline-info": color === "info",
                      "pr-10": !!iconName && iconPlacement === "right",
                      "pl-10": !!iconName && iconPlacement === "left",
                    },
                    inputClassName,
                  )}
                />

                {iconName && (
                  <Icon
                    name={iconName}
                    className={cn(
                      "absolute top-1/2 h-5 w-5 -translate-y-1/2 text-base-500",
                      {
                        "left-3.5": iconPlacement === "left",
                        "right-3.5": iconPlacement === "right",
                        "peer-focus:text-primary": color === "primary",
                        "peer-focus:text-secondary": color === "secondary",
                        "peer-focus:text-success": color === "success",
                        "peer-focus:text-warning": color === "warning",
                        "text-error": error,
                        "peer-focus:text-info": color === "info",
                      },
                      iconClassName,
                    )}
                  />
                )}
              </div>
            </div>
          );
        }}
        name={name}
        rules={rules}
        control={control}
      />
      <DateSelectDialog
        value={field.value}
        label={label}
        id={id}
        onChange={(value) => {
          field.onChange(value);
        }}
      />
    </>
  );
}

DateSelectField.propTypes = {
  rules: PropTypes.object,
  placeholder: PropTypes.string,
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  iconName: PropTypes.string,
  control: PropTypes.object.isRequired,
  iconPlacement: PropTypes.oneOf(["left", "right"]),
  iconClassName: PropTypes.string,
  inputClassName: PropTypes.string,
  className: PropTypes.string,
  required: PropTypes.bool,
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
  ]),
};
