import { useQueryClient } from "@tanstack/react-query";
import { Clock, TickCircle } from "iconsax-react";
import { AnimatePresence, motion } from "motion/react";
import PropTypes from "prop-types";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header";

const OrderFinalizationStep = ({ next, isOnlinePlan }) => {
  const queryClient = useQueryClient();

  const navigate = useNavigate();
  const [remainingTime, setRemainingTime] = useState(120);
  const [pollingActive, setPollingActive] = useState(true);
  const [completedItems, setCompletedItems] = useState(new Set());
  const [currentProcessingItem, setCurrentProcessingItem] = useState(-1);
  const intervalRef = useRef(null);

  const handleBack = useCallback(() => {
    navigate(-1, { replace: true });
  }, [navigate]);

  const checklistItems = [
    { text: "تحلیل وضعیت متابولیسم بدنی شما" },
    { text: "تحلیل بازه سنی، جنسیت و قد و وزن شما" },
    { text: "تحلیل درصد چربی، ماهیچه و آب بدن شما" },
    { text: "تحلیل شرایط سلامتی و بیماریهای شما" },
    { text: "تحلیل حساسیت های غذایی شما" },
    { text: "تحلیل سایر شرایط خاص شما" },
    { text: "تحلیل اهداف سلامتی شما" },
  ];

  // Timer for countdown
  useEffect(() => {
    if (isOnlinePlan && pollingActive) {
      intervalRef.current = setInterval(() => {
        setRemainingTime((prevTime) => {
          if (prevTime <= 1) {
            queryClient.invalidateQueries(api.Packages.list.queryKey);
            setPollingActive(false);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [pollingActive, isOnlinePlan, queryClient]);

  // Sequential item processing animation
  useEffect(() => {
    if (isOnlinePlan && pollingActive) {
      const totalDuration = 120; // 2 minutes
      const itemCount = checklistItems.length;
      const timePerItem = totalDuration / itemCount; // ~17 seconds per item
      const timeouts = [];

      let currentIndex = 0;

      const processNextItem = () => {
        if (currentIndex < itemCount && pollingActive) {
          setCurrentProcessingItem(currentIndex);

          // After processing time, mark as completed and move to next
          const completionTimeout = setTimeout(
            () => {
              setCompletedItems((prev) => new Set([...prev, currentIndex]));
              setCurrentProcessingItem(-1);
              currentIndex++;

              if (currentIndex < itemCount) {
                // Small delay before starting next item
                const nextTimeout = setTimeout(processNextItem, 500);
                timeouts.push(nextTimeout);
              }
            },
            timePerItem * 1000 - 500,
          ); // Subtract 500ms for the delay between items

          timeouts.push(completionTimeout);
        }
      };

      // Start processing first item after a short delay
      const initialTimeout = setTimeout(processNextItem, 1000);
      timeouts.push(initialTimeout);

      return () => {
        // Clear all timeouts
        timeouts.forEach((timeout) => clearTimeout(timeout));
      };
    }
  }, [isOnlinePlan, pollingActive, checklistItems.length]);

  // Reset states when component unmounts or polling stops
  useEffect(() => {
    if (!pollingActive) {
      setCompletedItems(new Set());
      setCurrentProcessingItem(-1);
    }
  }, [pollingActive]);

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const buttonText = useMemo(() => {
    if (isOnlinePlan && pollingActive) {
      return `منتظر گرفتن جواب از هوش مصنوعی (${formatTime(remainingTime)})`;
    }
    return "نهایی کردن سفارش برنامه غذایی";
  }, [isOnlinePlan, pollingActive, remainingTime, formatTime]);

  return (
    <>
      <Header className="sticky top-0" back={handleBack}>
        <span className="font-semibold">اطلاعات جسمانی</span>
      </Header>

      <div className="container flex grow flex-col py-4">
        <div className="card flex grow flex-col bg-base-100">
          <div className="card-body grow gap-5 p-4">
            <div className="flex grow flex-col items-center">
              <div className="relative mt-8 grid size-32 place-items-center">
                <img
                  className="absolute inset-0 h-full w-full animate-[spin_3s_linear_infinite_reverse] object-contain"
                  src="/app/assets/images/result/result1.png"
                  alt="Background"
                />

                <img
                  className="absolute size-20 transform object-contain"
                  src="/app/assets/images/result/result2.png"
                  alt="Foreground Icon"
                />
              </div>
              <span className="my-4 px-5 text-center font-bold text-lg text-primary">
                آماده سازی برای طراحی برنامه غذایی اختصاصی شما
              </span>
              <div className="w-full space-y-3 px-4">
                {checklistItems.map((item, index) => {
                  const isCompleted = completedItems.has(index);
                  const isProcessing = currentProcessingItem === index;

                  return (
                    <div
                      key={item.text}
                      className="flex items-center gap-3 text-base-content"
                    >
                      <motion.span
                        initial={{ opacity: 0.3, scale: 0.8 }}
                        animate={{
                          opacity: isCompleted ? 1 : isProcessing ? 0.8 : 0.3,
                          scale: isCompleted ? 1 : isProcessing ? 1.1 : 0.8,
                          color: isCompleted
                            ? "oklch(var(--su))"
                            : isProcessing
                              ? "oklch(var(--wa))"
                              : "oklch(var(--bc))",
                        }}
                        transition={{
                          duration: 0.5,
                          scale: {
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                          },
                        }}
                      >
                        <AnimatePresence mode="wait">
                          {isProcessing ? (
                            <motion.div
                              key="processing"
                              initial={{ rotate: 0 }}
                              animate={{ rotate: 360 }}
                              exit={{ opacity: 0 }}
                              transition={{
                                duration: 2,
                                repeat: Number.POSITIVE_INFINITY,
                                ease: "linear",
                              }}
                            >
                              <Clock className="size-5" />
                            </motion.div>
                          ) : (
                            <motion.div
                              key="tick"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{
                                type: "spring",
                                stiffness: 500,
                                damping: 15,
                              }}
                            >
                              <TickCircle className="size-5" />
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.span>
                      <motion.span
                        initial={{ opacity: 0.3 }}
                        animate={{
                          opacity: isCompleted ? 1 : isProcessing ? 0.9 : 0.4,
                          color: isCompleted
                            ? "oklch(var(--bc))"
                            : isProcessing
                              ? "oklch(var(--bc))"
                              : "oklch(var(--bc) / 0.6)",
                        }}
                        transition={{ duration: 0.5 }}
                        className={`font-medium text-sm ${
                          isProcessing ? "font-semibold" : ""
                        }`}
                      >
                        {item.text}
                      </motion.span>
                    </div>
                  );
                })}
              </div>

              <div className="sticky bottom-5 mt-auto w-full pt-5 text-center">
                {isOnlinePlan ? (
                  buttonText
                ) : (
                  <button
                    type="button"
                    onClick={next}
                    className="btn btn-primary w-full font-semibold text-base"
                  >
                    نهایی کردن سفارش برنامه غذایی
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

OrderFinalizationStep.propTypes = {
  next: PropTypes.func, // Changed to not required
  isOnlinePlan: PropTypes.bool,
};

export default OrderFinalizationStep;
