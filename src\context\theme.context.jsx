import PropTypes from 'prop-types';
import { createContext, useContext, useEffect, useState } from 'react';

export const ThemeContext = createContext({
  title: 'صفحه اصلی',
  breadcrumbs: [],
  setTitle: (title) => {},
  settings: null,
  setSettings: (settings) => {},
});

export default function ThemeContextProvider({ children }) {
  const context = useContext(ThemeContext);
  const [state, setState] = useState(context);

  function useSetTitle(title, breadcrumbs = [], data = true) {
    useEffect(() => {
      if (data) {
        setState({ ...state, title, breadcrumbs });
        document.title = `ویدان - ${title}`;
      }
    }, [data]);
  }

  function setSettings(data) {
    setState((prev) => ({ ...prev, settings: data }));
  }

  return (
    <ThemeContext.Provider
      value={{ ...state, setTitle: useSetTitle, setSettings }}>
      <div>{children}</div>
    </ThemeContext.Provider>
  );
}

ThemeContextProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export function useTheme() {
  return useContext(ThemeContext);
}
