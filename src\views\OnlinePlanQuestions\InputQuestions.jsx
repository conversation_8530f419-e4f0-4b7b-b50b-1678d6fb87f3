import PropTypes from "prop-types";
import { Input } from "react-daisyui";
import { useController } from "react-hook-form";
import { cn, convertInvalidCharacter } from "../../utils/utils";

const InputQuestions = ({
  control,
  name,
  rules,
  label,
  placeholder,
  hint,
  className,
  inputClassName,
  type = "text",
  max,
  min,
  required,
  ...props
}) => {
  const mergeRules = {
    required: {
      value: required,
      message: "این سوال الزامی است.",
    },
    ...rules,
    ...(type === "number"
      ? {
          validate: {
            isNumber: (val) =>
              !val || /^\d+$/.test(val) || "فقط اعداد انگلیسی مجاز اند",
            minValue: (val) =>
              min == null || +val >= min || `مقدار باید حداقل ${min} باشد`,
            maxValue: (val) =>
              max == null || +val <= max || `مقدار نباید بیشتر از ${max} باشد`,
          },
        }
      : {}),
  };

  const {
    field: { onChange, value, ...field },
    fieldState: { error },
  } = useController({
    control,
    name,
    rules: mergeRules,
  });

  const handleInvalidCharacter = (val) => {
    let newValue;
    if (type === "number") {
      newValue = convertInvalidCharacter(val.replace(/[^\d]/g, ""));
    } else {
      newValue = convertInvalidCharacter(val);
    }
    if (newValue !== value) {
      onChange(newValue);
    }
  };

  return (
    <div className={cn("my-3 flex w-full flex-col gap-3", className)}>
      {label && (
        <span className="text-sm font-medium text-base-500">{label}</span>
      )}
      <Input
        {...field}
        type="text"
        value={value || ""}
        inputMode={type === "number" ? "numeric" : undefined}
        pattern={type === "number" ? "\\d*" : undefined}
        placeholder={placeholder}
        onChange={(e) => {
          handleInvalidCharacter(e.target.value);
        }}
        className={cn(
          "w-full max-w-60 self-center rounded-xl text-center text-lg",
          {
            "border-error": error,
          },
          inputClassName,
        )}
        {...props}
      />
      {hint && (
        <span className="text-xs text-base-400 text-center">{hint}</span>
      )}
      {error && (
        <span className="text-xs text-error text-center">{error.message}</span>
      )}
    </div>
  );
};

InputQuestions.propTypes = {
  control: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  rules: PropTypes.object,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  hint: PropTypes.string,
  required: PropTypes.bool,
  className: PropTypes.string,
  inputClassName: PropTypes.string,
  type: PropTypes.oneOf(["text", "number"]),
  max: PropTypes.number,
  min: PropTypes.number,
};

export default InputQuestions;
