import { useQueryClient } from "@tanstack/react-query";
import { ArrowLeft2, ArrowRight2 } from "iconsax-react";
import PropTypes from "prop-types";
import { Fragment } from "react";
import { Textarea } from "react-daisyui";
import { Controller, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import api from "../../api";
import DateSelectField from "../Components/Form/DateSelectField";
import Icon from "../Components/Icon";
import ChoiceQuestions from "./ChoiceQuestions";
import InputQuestions from "./InputQuestions";

const QuestionsLayout = ({ data }) => {
  const { control, handleSubmit, setValue, watch, reset } = useForm({
    mode: "onSubmit",
  });

  const queryClient = useQueryClient();

  const { mutate } = api.MealPlans.questions.add.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries(api.MealPlans.questions.detail.queryKey);
      reset();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const submitHandler = (formData) => {
    const formattedData = data?.questions
      ?.map((question) => {
        const q_id = question.id;
        const descriptiveAnswer = formData[`Q${q_id}_descriptive`];
        const choiceAnswers = formData[`Q${q_id}`];

        if (
          question?.type?.key === "MULTIPLE_CHOICE_DESCRIPTIVE" ||
          question?.type?.key === "ONE_CHOICE_DESCRIPTIVE"
        ) {
          if (descriptiveAnswer) {
            return {
              q_id: q_id,
              answer: descriptiveAnswer,
            };
          }
          if (Array.isArray(choiceAnswers)) {
            return {
              q_id: q_id,
              answer: choiceAnswers.join(","),
            };
          }
          if (choiceAnswers) {
            return {
              q_id: q_id,
              answer: choiceAnswers,
            };
          }
        } else if (Array.isArray(choiceAnswers)) {
          return {
            q_id: q_id,
            answer: choiceAnswers.join(","),
          };
        }
        return {
          q_id: q_id,
          answer: formData[`Q${q_id}`],
        };
      })
      .filter(Boolean); // Filter out any undefined or null entries if a question type is not handled

    // console.log(formData);
    mutate(formattedData);
  };

  return (
    <div className="container flex grow flex-col">
      <div className="card flex grow flex-col bg-base-100">
        <div className="card-body grow gap-5 px-4 py-4">
          <div className="flex grow flex-col items-start justify-start gap-3">
            {data?.interactive && (
              <div className="relative flex min-h-32 w-full items-center justify-center rounded-lg bg-primary-focus/20 p-4 text-center">
                <img
                  className="absolute end-0 top-2 max-h-4"
                  src="/app/assets/images/background/Thunder.png"
                  alt=""
                />

                <p className="font-medium text-primary-focus">
                  {data.interactive?.text}
                </p>

                <img
                  className="absolute -bottom-1 -start-1"
                  src="/app/assets/images/background/QuarterCircle.png"
                  alt=""
                />
              </div>
            )}

            <div className="mt-2 flex w-full items-start justify-start gap-2">
              <Icon name="ampoule" className="size-5 text-secondary" />
              <h3 className="text-lg font-medium text-base-content">
                {data?.step?.title}
              </h3>
            </div>

            {data?.questions?.map((question) => {
              return (
                <Fragment key={question?.id}>
                  <h4 className="font-medium text-base-content">
                    {question?.title} {question?.optional && "(اختیاری)"}
                  </h4>

                  {question?.type?.key === "ONE_CHOICE" && (
                    <ChoiceQuestions
                      control={control}
                      // orientation={
                      //   question?.answers?.length > 2
                      //     ? "vertical"
                      //     : "horizontal"
                      // }
                      required={!question?.optional}
                      name={`Q${question?.id}`}
                      items={question?.answers?.map((answer) => ({
                        label: answer.title,
                        value: answer.id,
                        inputName: `Q${question?.id}`,
                      }))}
                    />
                  )}
                  {question?.type?.key === "MULTIPLE_CHOICE" && (
                    <ChoiceQuestions
                      control={control}
                      required={!question?.optional}
                      name={`Q${question?.id}`}
                      type="checkbox"
                      items={question.answers.map((answer) => ({
                        label: answer.title,
                        value: answer.id,
                        inputName: `Q${question?.id}`,
                      }))}
                    />
                  )}
                  {question?.type?.key === "DATE" && (
                    <DateSelectField
                      control={control}
                      name={`Q${question?.id}`}
                      label={question?.rules?.label || "تاریخ"}
                      placeholder={
                        question?.rules?.placholder ||
                        question?.rules?.placeholder ||
                        ""
                      }
                      iconName="calendar"
                      color="secondary"
                      required={!question?.optional}
                      className="px-8"
                    />
                  )}
                  {question?.type?.key === "DESCRIPTIVE" && (
                    <InputQuestions
                      control={control}
                      name={`Q${question?.id}`}
                      required={!question?.optional}
                      type={question?.rules?.type || "text"}
                      placeholder={
                        question?.rules?.placholder ||
                        question?.rules?.placeholder ||
                        ""
                      }
                      label={question?.rules?.label || ""}
                      hint={question?.rules?.hint || ""}
                    />
                  )}
                  {question?.type?.key === "ONE_CHOICE_DESCRIPTIVE" && (
                    <>
                      <ChoiceQuestions
                        control={control}
                        required={
                          !question?.optional &&
                          !watch(`Q${question?.id}_descriptive`)
                        }
                        name={`Q${question?.id}`}
                        type="radio"
                        items={question.answers.map((answer) => ({
                          label: answer.title,
                          value: answer.id,
                          inputName: `Q${question?.id}`,
                        }))}
                        onChange={() =>
                          setValue(`Q${question?.id}_descriptive`, "")
                        }
                      />
                      <div className="w-full px-4">
                        <Controller
                          control={control}
                          name={`Q${question?.id}_descriptive`}
                          render={({ field }) => (
                            <Textarea
                              {...field}
                              placeholder={
                                question?.rules?.placholder ||
                                question?.rules?.placeholder ||
                                "سایر"
                              }
                              className="w-full rounded-md border-2 border-base-300"
                              onChange={(e) => {
                                field.onChange(e);
                                setValue(`Q${question?.id}`, "");
                              }}
                            />
                          )}
                        />
                      </div>
                    </>
                  )}
                  {question?.type?.key === "MULTIPLE_CHOICE_DESCRIPTIVE" && (
                    <>
                      <ChoiceQuestions
                        control={control}
                        required={
                          !question?.optional &&
                          !watch(`Q${question?.id}_descriptive`)
                        }
                        name={`Q${question?.id}`}
                        type="checkbox"
                        items={question.answers.map((answer) => ({
                          label: answer.title,
                          value: answer.id,
                          inputName: `Q${question?.id}`,
                        }))}
                        onChange={() =>
                          setValue(`Q${question?.id}_descriptive`, "")
                        }
                      />
                      <div className="w-full px-4">
                        <Controller
                          control={control}
                          name={`Q${question?.id}_descriptive`}
                          render={({ field }) => (
                            <Textarea
                              {...field}
                              placeholder={
                                question?.rules?.placholder ||
                                question?.rules?.placeholder ||
                                "سایر"
                              }
                              className="w-full rounded-md border-2 border-base-300"
                              onChange={(e) => {
                                field.onChange(e);
                                setValue(`Q${question?.id}`, []);
                              }}
                            />
                          )}
                        />
                      </div>
                    </>
                  )}
                </Fragment>
              );
            })}

            <div className="mt-auto flex w-full items-center gap-2">
              <button className="btn btn-secondary aspect-square shrink-0 rounded-full px-0 py-0">
                <ArrowRight2 className="size-5" />
              </button>
              <button
                className="btn btn-primary grow"
                onClick={handleSubmit(submitHandler)}
              >
                سوال بعدی
                <ArrowLeft2 className="size-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

QuestionsLayout.propTypes = {
  data: PropTypes.object.isRequired,
};

export default QuestionsLayout;
